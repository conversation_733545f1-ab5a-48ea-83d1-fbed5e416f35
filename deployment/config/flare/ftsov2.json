{"contractName": "FtsoV2PriceStore", "firstVotingRoundStartTs": **********, "votingEpochDurationSeconds": 90, "trustedProviders": ["******************************************", "******************************************", "******************************************"], "trustedProvidersThreshold": 2, "maxSpreadBIPS": 100, "feeds": [{"feedId": "FLR/USD", "symbol": "FLR", "feedDecimals": 6}, {"feedId": "BTC/USD", "symbol": "BTC", "feedDecimals": 2}, {"feedId": "XRP/USD", "symbol": "XRP", "feedDecimals": 5}, {"feedId": "DOGE/USD", "symbol": "DOGE", "feedDecimals": 5}, {"feedId": "ETH/USD", "symbol": "ETH", "feedDecimals": 3}, {"feedId": "USDC/USD", "symbol": "USDC", "feedDecimals": 5}, {"feedId": "USDT/USD", "symbol": "USDT", "feedDecimals": 5}]}