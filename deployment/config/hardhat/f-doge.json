{"$schema": "../asset-manager-parameters.schema.json", "burnAddress": "0x000000000000000000000000000000000000dEaD", "chainName": "DOGE", "assetName": "<PERSON><PERSON><PERSON><PERSON>", "assetSymbol": "DOGE", "assetDecimals": 8, "fAssetName": "F-Dog<PERSON><PERSON>n", "fAssetSymbol": "FDOGE", "poolTokenSuffix": "DOGE", "assetMintingDecimals": 8, "poolCollateral": {"token": "WNat", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testDOGE", "tokenFtsoSymbol": "NAT", "minCollateralRatioBIPS": 20000, "safetyMinCollateralRatioBIPS": 21000}, "vaultCollaterals": [{"token": "testUSDC", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testDOGE", "tokenFtsoSymbol": "testUSDC", "minCollateralRatioBIPS": 14000, "safetyMinCollateralRatioBIPS": 15000}, {"token": "testUSDT", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testDOGE", "tokenFtsoSymbol": "testUSDT", "minCollateralRatioBIPS": 15000, "safetyMinCollateralRatioBIPS": 16000}], "mintingCap": "0", "lotSize": "1000 00000000", "collateralReservationFeeBIPS": 200, "mintingPoolHoldingsRequiredBIPS": 5000, "maxRedeemedTickets": 20, "redemptionFeeBIPS": 100, "redemptionDefaultFactorVaultCollateralBIPS": 12500, "underlyingBlocksForPayment": 100, "underlyingSecondsForPayment": 3600, "attestationWindowSeconds": 86400, "averageBlockTimeMS": 2000, "confirmationByOthersAfterSeconds": 7200, "confirmationByOthersRewardUSD5": "100 00000", "paymentChallengeRewardBIPS": 0, "paymentChallengeRewardUSD5": "300 00000", "liquidationStepSeconds": 90, "liquidationCollateralFactorBIPS": [12000, 16000, 20000], "liquidationFactorVaultCollateralBIPS": [10000, 10000, 10000], "maxTrustedPriceAgeSeconds": 600, "withdrawalWaitMinSeconds": 60, "vaultCollateralBuyForFlareFactorBIPS": 10500, "minUpdateRepeatTimeSeconds": 86400, "tokenInvalidationTimeMinSeconds": 86400, "agentExitAvailableTimelockSeconds": 86400, "agentFeeChangeTimelockSeconds": 86400, "agentMintingCRChangeTimelockSeconds": 3600, "poolExitCRChangeTimelockSeconds": 86400, "agentTimelockedOperationWindowSeconds": 3600, "collateralPoolTokenTimelockSeconds": 60, "diamondCutMinTimelockSeconds": 7200, "maxEmergencyPauseDurationSeconds": 86400, "emergencyPauseDurationResetAfterSeconds": 604800, "redemptionPaymentExtensionSeconds": 30, "coreVaultNativeAddress": "0xfeC5BF0a64963E6b2EbA8153235E40f9e5a8c8Ae", "coreVaultTransferTimeExtensionSeconds": 7200, "coreVaultMinimumAmountLeftBIPS": 2000, "coreVaultRedemptionFeeBIPS": 0, "coreVaultMinimumRedeemLots": 10}