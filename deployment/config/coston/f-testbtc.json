{"$schema": "../asset-manager-parameters.schema.json", "burnAddress": "******************************************", "chainName": "testBTC", "assetName": "Test BTC", "assetSymbol": "testBTC", "assetDecimals": 8, "fAssetName": "FBTC", "fAssetSymbol": "FTestBTC", "poolTokenSuffix": "TBTC", "assetMintingDecimals": 8, "poolCollateral": {"token": "WNat", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testBTC", "tokenFtsoSymbol": "CFLR", "minCollateralRatioBIPS": 20000, "safetyMinCollateralRatioBIPS": 21000}, "vaultCollaterals": [{"token": "testUSDC", "decimals": 6, "directPricePair": false, "assetFtsoSymbol": "testBTC", "tokenFtsoSymbol": "testUSDC", "minCollateralRatioBIPS": 14000, "safetyMinCollateralRatioBIPS": 15000}, {"token": "testUSDT", "decimals": 6, "directPricePair": false, "assetFtsoSymbol": "testBTC", "tokenFtsoSymbol": "testUSDT", "minCollateralRatioBIPS": 14000, "safetyMinCollateralRatioBIPS": 15000}, {"token": "testETH", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testBTC", "tokenFtsoSymbol": "testETH", "minCollateralRatioBIPS": 14000, "safetyMinCollateralRatioBIPS": 15000}], "mintingCap": "0", "lotSize": "100000", "collateralReservationFeeBIPS": 10, "mintingPoolHoldingsRequiredBIPS": 5000, "maxRedeemedTickets": 20, "redemptionFeeBIPS": 100, "redemptionDefaultFactorVaultCollateralBIPS": 11000, "underlyingBlocksForPayment": 10, "underlyingSecondsForPayment": 7200, "averageBlockTimeMS": 600000, "attestationWindowSeconds": 86400, "confirmationByOthersAfterSeconds": 14400, "confirmationByOthersRewardUSD5": "100 00000", "paymentChallengeRewardBIPS": 0, "paymentChallengeRewardUSD5": "300 00000", "liquidationStepSeconds": 180, "liquidationCollateralFactorBIPS": [10500, 11000, 11500], "liquidationFactorVaultCollateralBIPS": [10000, 10000, 10000], "maxTrustedPriceAgeSeconds": 600, "withdrawalWaitMinSeconds": 60, "vaultCollateralBuyForFlareFactorBIPS": 10000, "minUpdateRepeatTimeSeconds": 60, "tokenInvalidationTimeMinSeconds": 86400, "agentExitAvailableTimelockSeconds": 60, "agentFeeChangeTimelockSeconds": 120, "agentMintingCRChangeTimelockSeconds": 120, "poolExitCRChangeTimelockSeconds": 120, "agentTimelockedOperationWindowSeconds": 3600, "collateralPoolTokenTimelockSeconds": 60, "diamondCutMinTimelockSeconds": 7200, "maxEmergencyPauseDurationSeconds": 86400, "emergencyPauseDurationResetAfterSeconds": 604800, "redemptionPaymentExtensionSeconds": 60, "coreVaultNativeAddress": "0xfeC5BF0a64963E6b2EbA8153235E40f9e5a8c8Ae", "coreVaultTransferTimeExtensionSeconds": 7200, "coreVaultMinimumAmountLeftBIPS": 2000, "coreVaultRedemptionFeeBIPS": 0, "coreVaultMinimumRedeemLots": 10}