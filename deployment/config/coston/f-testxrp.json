{"$schema": "../asset-manager-parameters.schema.json", "burnAddress": "0x000000000000000000000000000000000000dEaD", "chainName": "testXRP", "assetName": "Test XRP", "assetSymbol": "testXRP", "assetDecimals": 6, "fAssetName": "FXRP", "fAssetSymbol": "FTestXRP", "poolTokenSuffix": "TXRP", "assetMintingDecimals": 6, "poolCollateral": {"token": "WNat", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testXRP", "tokenFtsoSymbol": "CFLR", "minCollateralRatioBIPS": 20000, "safetyMinCollateralRatioBIPS": 21000}, "vaultCollaterals": [{"token": "testUSDC", "decimals": 6, "directPricePair": false, "assetFtsoSymbol": "testXRP", "tokenFtsoSymbol": "testUSDC", "minCollateralRatioBIPS": 14000, "safetyMinCollateralRatioBIPS": 15000}, {"token": "testUSDT", "decimals": 6, "directPricePair": false, "assetFtsoSymbol": "testXRP", "tokenFtsoSymbol": "testUSDT", "minCollateralRatioBIPS": 14000, "safetyMinCollateralRatioBIPS": 15000}, {"token": "testETH", "decimals": 18, "directPricePair": false, "assetFtsoSymbol": "testXRP", "tokenFtsoSymbol": "testETH", "minCollateralRatioBIPS": 14000, "safetyMinCollateralRatioBIPS": 15000}], "mintingCap": "0", "lotSize": "20 000000", "collateralReservationFeeBIPS": 10, "mintingPoolHoldingsRequiredBIPS": 5000, "maxRedeemedTickets": 20, "redemptionFeeBIPS": 10, "redemptionDefaultFactorVaultCollateralBIPS": 11000, "underlyingBlocksForPayment": 500, "underlyingSecondsForPayment": 900, "averageBlockTimeMS": 2000, "attestationWindowSeconds": 86400, "confirmationByOthersAfterSeconds": 7200, "confirmationByOthersRewardUSD5": "100 00000", "paymentChallengeRewardBIPS": 0, "paymentChallengeRewardUSD5": "300 00000", "liquidationStepSeconds": 180, "liquidationCollateralFactorBIPS": [10500, 11000, 11500], "liquidationFactorVaultCollateralBIPS": [10000, 10000, 10000], "maxTrustedPriceAgeSeconds": 600, "withdrawalWaitMinSeconds": 60, "vaultCollateralBuyForFlareFactorBIPS": 10000, "minUpdateRepeatTimeSeconds": 60, "tokenInvalidationTimeMinSeconds": 86400, "agentExitAvailableTimelockSeconds": 60, "agentFeeChangeTimelockSeconds": 120, "agentMintingCRChangeTimelockSeconds": 120, "poolExitCRChangeTimelockSeconds": 120, "agentTimelockedOperationWindowSeconds": 3600, "collateralPoolTokenTimelockSeconds": 60, "diamondCutMinTimelockSeconds": 7200, "maxEmergencyPauseDurationSeconds": 86400, "emergencyPauseDurationResetAfterSeconds": 604800, "redemptionPaymentExtensionSeconds": 30, "coreVaultNativeAddress": "0xfeC5BF0a64963E6b2EbA8153235E40f9e5a8c8Ae", "coreVaultTransferTimeExtensionSeconds": 7200, "coreVaultMinimumAmountLeftBIPS": 2000, "coreVaultRedemptionFeeBIPS": 0, "coreVaultMinimumRedeemLots": 10}