[{"name": "Price<PERSON><PERSON><PERSON>", "contractName": "FtsoV2PriceStore.sol", "addresses": ["0x85973FA0098F5471e63C2c3FFa3ec8956eDEDF73"]}, {"name": "FtsoV2PriceStore", "contractName": "FtsoV2PriceStore.sol", "addresses": ["0x85973FA0098F5471e63C2c3FFa3ec8956eDEDF73"]}, {"name": "AgentOwnerRegistry", "contractName": "AgentOwnerRegistry.sol", "addresses": ["0xa7f5d3C81f55f2b072FB62a0D4A03317BFd1a3c0"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contractName": "Whitelist.sol", "addresses": ["0xd5d90c61cF30B86b1c4507515e91470334d77Ad6"]}, {"name": "AgentVaultProxyImplementation", "contractName": "AgentVault.sol", "addresses": ["0x279615157757FecfA7B24Cc285A8970A12E68CCA", "0x5E3b6e722282E017f11ac2aB8022455A7167559B"]}, {"name": "AgentVaultFactory", "contractName": "AgentVaultFactory.sol", "addresses": ["0x0f3B379f36A4854ae0ce52E363d5A3dc9c1CFee7", "0x088c03dda1214d5cAA61070D562504B49E23CA36"]}, {"name": "CollateralPoolProxyImplementation", "contractName": "CollateralPool.sol", "addresses": ["0xa9fF7ff60119D33e30814BbF91ba8613a3d4F8Ec", "0x6fb1fFF5bD9cFCFed37Ff7aF7c72eB6dFA8f6eE1"]}, {"name": "CollateralPoolFactory", "contractName": "CollateralPoolFactory.sol", "addresses": ["0x052C7Cb58FBd5964940D7a10cA78C50A90bAD3A7", "0xE803a8Fc4369068FA1d7dDd72b7F6d21E44Eb190"]}, {"name": "CollateralPoolTokenProxyImplementation", "contractName": "CollateralPoolToken.sol", "addresses": ["0x8cde2269953B2f08102aE23ea86F2853e3446912"]}, {"name": "CollateralPoolTokenFactory", "contractName": "CollateralPoolTokenFactory.sol", "addresses": ["0xeed6C3E791e8d3d939e6A50811D8dB3e1e5693BE"]}, {"name": "AssetManagerControllerImplementation", "contractName": "AssetManagerController.sol", "addresses": ["0x5bcb8a5d93474126A184e8706e26dc8B086082EC", "0x08afa05a5e73820d3b3978DB5295eD2Dee482fce", "0x92C12B3E795185dCC820d9B41Af38dA9449b3484"]}, {"name": "AssetManagerController", "contractName": "AssetManagerControllerProxy.sol", "addresses": ["0xFA1D82438412820320F79515281a79214529dd42", "0x2920E7953F0EA8871B90F04E213dF894bB1CD456", "0xFeA3Bf40B5a75fad7b4e2CBEcEE2Bd4eE899698e", "0x04025BBf6Be30d6cB6ad412c2958c712c6B2BCbA"]}, {"name": "FAssetImplementation", "contractName": "FAsset.sol", "addresses": ["0xEf98Eb01a374b2FC5eA877ed6933083f43f7923A", "0x73ACEbf1ad0D05c14d579661C0998A35AE028ad7", "0x80532F229E507f8642b4136168dC88a9f24cC5D3"]}, {"name": "AssetManagerInit", "contractName": "AssetManagerInit.sol", "addresses": ["0xA9ED6c50FeF1D3cA2B7C03825Dc32605a357f1d3", "0xD5a79Ee4F36fdd0355a7AE87D2fc9cF591EAe117"]}, {"name": "AssetManagerDiamondCutFacet", "contractName": "AssetManagerDiamondCutFacet.sol", "addresses": ["0xC75fa79dfeAe94b43Fa6357CD274AD6d23475116", "0x6204D457e77c40Fa43f8460336B592627d79B5E8"]}, {"name": "DiamondLoupeFacet", "contractName": "DiamondLoupeFacet.sol", "addresses": ["0x8173657A1ED01592228F1808Cb384267715B149c"]}, {"name": "AgentInfoFacet", "contractName": "AgentInfoFacet.sol", "addresses": ["0xE42Dc14d58628a1658A70EcfF2fD51e73898832b", "0xDc658B7656F3c72BDA74a66B8c528e0494B358eB", "0x8a97cfDb732c8D214eD6C3F8352Ef270955428f6", "0xdd99Bf51957a3b479445ea95F01a4d4eb663B7a2", "0xA8e8f5620cBbA06fCbbB46D3eB222AFc25f3c698"]}, {"name": "AvailableAgentsFacet", "contractName": "AvailableAgentsFacet.sol", "addresses": ["0xBAFcb34048fFcc79627411b0Ce8196841D4a8BEE", "0xc11CCc3705E9D6ec51119eCB9c21168426CAD5cf", "0xadCe6A1E925590A497c12aC934F900CEe32a7E96", "0xE6eFe5119576289073ce8425cF39256aDC16880B", "0x3663775E34D691F590a3F1A83446353144229082"]}, {"name": "CollateralReservationsFacet", "contractName": "CollateralReservationsFacet.sol", "addresses": ["0xe291983b9F6E6f0f28d8f0904a6FBA86c7b64C35", "0xa7a04172BeBecC59ee219a162f043d37F370c452", "0x34FE1b0e3fEf64039Dce8045Bde76aE97F2eDD05", "0x483cB718025cC01b0008679E6520A91A06D3125A", "0xB4462688e6061C843Fe365b6fa386Fb37aa5B740", "0x32726aD6932eD37c7Adc1115903Bc3B6476146f7", "0x6B4f4032C9C739946F4862B2196F899b1045FA02", "0x7c6b4a2b1B9F0bc01AC9437DB804e5b0456a4cE3"]}, {"name": "MintingFacet", "contractName": "MintingFacet.sol", "addresses": ["0x2b3a2D8529C51Ca8555c3ac31C031413b5f7dD5E", "0x02099c45ECe034BB1D7869cF5CBfD7676549BAC7", "0x2f53166Bd95ADe05b2739eEF50E21FF9DDd9b1a3", "0x915D3fD831f672b0212142c35F6De16a8203dBee", "0x34B8Eb0DD928146F973CAb6E7b3b8C28F9833Ef4", "0x7b043ABeE86bCA54Db4616FE488871D64a7e14A5", "0x3F042f092092Ea52169e8d79A39B65f147Be2983", "0xBd2279ec577148e228Efc888bf65674b7DAdED48", "0xDeE72B9826817265B5445a0C784034c3d8BedfA0"]}, {"name": "RedemptionRequestsFacet", "contractName": "RedemptionRequestsFacet.sol", "addresses": ["0x2B6923C03615AeB8616c7C683D76BD43Ebd66B3f", "0x59B92f064bEd979A2F4E47a2A3ABCbCda91e2AE4", "0x73bc711Ec8e157f1ADA60D694415ccbf8A560a05", "0xc5530AB630127b4F3c793fA3BE2fDc2A79Fc76b1", "0xa5e9c2DC2f514B67e404CFfe383AEC3d6740de9E", "0x43Eff32ACCDd9e7E79f7e90518C30BaB41F00Fb1", "0x6fa01b66aA8bAE9A1A806642bea896fCC6c191d9", "0x64EFa04419Fe8F40aA364C0337abDD4A8572e8B3"]}, {"name": "RedemptionConfirmationsFacet", "contractName": "RedemptionConfirmationsFacet.sol", "addresses": ["0x1D83f1B26025acF8bbE13E972337000bD9f3fA21", "0x9A56c590Ea5dA91708085fE0C3De8DBeC22b58A7", "0xe7bBAbb802F51f5ce6750792E2B42630acbbe2d3", "0x9E93672e3437C5d77e547DD3f5F604cA79eB69e2", "0x723298a338F4Df4825Ce99A539079fBf674e621f", "0x2d6976e397D033125FBA2e278bbE986bCe496BCf", "0x3E7575DA7eC4923c59945B8bBfBB3f2EEa6d3807", "0xfDc2795124795dF90ba9a7299700Ae97dC17764A"]}, {"name": "RedemptionDefaultsFacet", "contractName": "RedemptionDefaultsFacet.sol", "addresses": ["0x722Af818Dc84179795aEfeA83f23f1Fa9b1D148E", "0x769A7cd19944B02Cf2B1FE193ba69ddB51A1526B", "0xD4C4C07ea09893ae9fe332A18923D5d2c82Df558", "0x1EF128E6D6fefc04dB3D84162b81388C78473C04", "0xEa262921be98300C380cA6e96C7252A8b87557f1", "0x1a85cA9568BA9aC45A2254eeb137C372f6f1222d", "0x75c6e82C4034321eD26dAa121e01aD213dea6f1F"]}, {"name": "RedemptionHandshakeFacet", "contractName": "RedemptionHandshakeFacet.sol", "addresses": ["0xb66D244256c75Ca794818b0d10A0a90eD4d518F4", "0xE9cca5f6e70678E74860FdC238A6d865Fe24a332", "0x1c59e81d3CBa35a342Bc25ec980D0D3737145Fef", "0x11022ed31b9265A0f3f87FC6828362bb1DE62664", "0x5c93a5EA6EA9E412812cD48052A1bf7a6303bceD", "0xdD762D03dfF8317Ca7b53172c20d6C239B319bE2"]}, {"name": "LiquidationFacet", "contractName": "LiquidationFacet.sol", "addresses": ["0xdf6446B037de041f5FF310E18D444da265C15902", "0xad07eaE2ab1370F52C1209A372CBD57fE5a9F344", "0x2b92F2eCd021d2D0ee595761ebdD71b2C61aE65f", "0x0e20FF5B20f6EeD5cbD6b656C9df6E6b98F257F2", "0x66B2285698D7F16d0A6464799A4cD404dBf788B5"]}, {"name": "ChallengesFacet", "contractName": "ChallengesFacet.sol", "addresses": ["0x8f679caE284FA5a7760c90eE55d10515f4B55FED", "0x84814d2860C0Ffc1B1C8B70B292e09e5c739d9B4", "0xE803fc93EE3651CA347FCe7025217027820ADDB9", "0xFc4a6C47CC69dE299bda055BE499553413CdA61F", "0xB2A618d52Ea70B53DAd347F920CDB8892feCb333", "0xb6cF88FdaB9c87d5B472D656270d27384e541109"]}, {"name": "UnderlyingBalanceFacet", "contractName": "UnderlyingBalanceFacet.sol", "addresses": ["0x3A09E2f4D39175Ff3158063C727e3c93FEe18666", "0xF7d9CE6d4E021E6a92757fA13a4feC67f982506c", "0xF882F870Cc4D04af0ffE4390e9F0bdC27Edba2c1", "0xAc7BD3374984152B7838758574000E4CDC1B5655"]}, {"name": "UnderlyingTimekeepingFacet", "contractName": "UnderlyingTimekeepingFacet.sol", "addresses": ["0xEdB866fa187950A50BF7729fF98e1578137B8b55", "0x8D3B8939978298264D3C6236121737ea143f64bC"]}, {"name": "AgentVaultManagementFacet", "contractName": "AgentVaultManagementFacet.sol", "addresses": ["0x55E34fE4318fB24F248cA645C077AC37a857cf32", "0x55A9aB03c04b5d11faB6924E34E7b411297356c2", "0xfA6441685D77e9AF1E73989EbADdfca02b9ff66E", "0xf082799bc2e116dDa37Acb28a77A8302824343f3", "0x1CBCa4aA9055b7A584123Dfcb255767adC454fff"]}, {"name": "AgentSettingsFacet", "contractName": "AgentSettingsFacet.sol", "addresses": ["0xD1a9BDDeC87ce59095DA9666d4D9bEe6cEC90139", "0x59010d527324A7684C309594c5feA7c70E017821", "0xdE60D9185068e61c4Ef4966449075f1Bc9fDF28E", "0x0980195509385f9AAf30E42FdA12F5958439bF56", "0xb85920b13b76E1691259Cf59B4EfD6fCBa679c1D"]}, {"name": "CollateralTypesFacet", "contractName": "CollateralTypesFacet.sol", "addresses": ["0xE18A50EE10eF1bDA2BC687761b365669Db35a6fD", "0xe5B1B6956B5A8F1F9389b5B2eD493E4cD5A8D1a5", "0x693ef87E01042e1350b1f528C9e6aB08Bfc9601b"]}, {"name": "AgentCollateralFacet", "contractName": "AgentCollateralFacet.sol", "addresses": ["0x5D81bBf3F9BC3e20CB6Ec14891774f486d17fAd6", "0x81e92E817FE0597F31c8b45614c2167712874786", "0x618AA8447aEC87DA22be8D6D622eCAFb491E0b29", "0xE166FE64AdE428296052dBe6d1De32C7907D7D12", "0x7bC9288Ae0c3FEd5dc5705E5dBF6C745d411c2d6"]}, {"name": "SettingsReaderFacet", "contractName": "SettingsReaderFacet.sol", "addresses": ["0x84dC9bDA71357dA9Faa8AAE597D97117F12F92EE", "0x7BC4BC25d3A0df58DD95bB190aD04e2a0C7935b8", "0x4E50966e8b085C29D8Dd19fAC79e431398B57C21", "0x851B60b30c7Bd337f7d7a40335A58CA8ae58dd5d"]}, {"name": "SettingsManagementFacet", "contractName": "SettingsManagementFacet.sol", "addresses": ["0x74AeAE208daec33A2462ACEa0a35C7A66a9d0604", "0xef8a9DFa12913E3b0549E1e2ac7F5e21363a5a01", "0x5eC27837a5C4d678F138B1610e091e37E989433D", "0x21Bb15eC8Db6c24630eBB3A8e61b54a65825aff9", "0x562DAe5C03bb77b3bed423ac933d4e1e4a1CB9A0"]}, {"name": "AgentVaultAndPoolSupportFacet", "contractName": "AgentVaultAndPoolSupportFacet.sol", "addresses": ["0x80F99d4fBDe31958edCEDfC29b334e7951A56666", "0x4A99CCBF7BF37b605aA014D5E54E7E00830E8f34"]}, {"name": "SystemStateManagementFacet", "contractName": "SystemStateManagementFacet.sol", "addresses": ["0xB8fC482E41Ee91a0CC3B58D21fc79350208Ea438", "0xd0582c3F7f7Aa62db9B47F6b600A35E97fa77D28"]}, {"name": "SystemInfoFacet", "contractName": "SystemInfoFacet.sol", "addresses": ["0x9b01Ae6E14F570Fe3535a91f6B863032e4D88662", "0x73410A3FdDd6886EcB75f10FE4705e5c06F0875b", "0xF28437D72abF6ad8b6E8DDA87bAFEA6E9B7827A4"]}, {"name": "EmergencyPauseFacet", "contractName": "EmergencyPauseFacet.sol", "addresses": ["0xFc1f1f5b3d2E0e22771d270F577799aE1FD7E3DF", "0xBdF02FE827596ee9B5f8fA3AcE9d497770E03FbE"]}, {"name": "AgentPingFacet", "contractName": "AgentPingFacet.sol", "addresses": ["0xbAd1ACC5733561BBaAbE4e45411F5a0289d614Ba", "******************************************"]}, {"name": "RedemptionTimeExtensionFacet", "contractName": "RedemptionTimeExtensionFacet.sol", "addresses": ["******************************************", "******************************************"]}, {"name": "TransferFeeFacet", "contractName": "TransferFeeFacet.sol", "addresses": ["******************************************", "******************************************", "******************************************"]}, {"name": "AssetManager_FBTC", "contractName": "AssetManager.sol", "addresses": ["******************************************", "******************************************", "******************************************"]}, {"name": "FBTC", "contractName": "FAssetProxy.sol", "addresses": ["******************************************", "******************************************", "******************************************"]}, {"name": "AssetManager_FDOGE", "contractName": "AssetManager.sol", "addresses": ["******************************************", "******************************************", "******************************************"]}, {"name": "FDOGE", "contractName": "FAssetProxy.sol", "addresses": ["******************************************", "******************************************", "******************************************"]}, {"name": "AssetManager_FXRP", "contractName": "AssetManager.sol", "addresses": ["******************************************", "******************************************", "******************************************"]}, {"name": "FXRP", "contractName": "FAssetProxy.sol", "addresses": ["******************************************", "******************************************", "0xF9a84f4ec903F4EaB117A9c1098BeC078BA7027d"]}, {"name": "EmergencyPauseTransfersFacet", "contractName": "EmergencyPauseTransfersFacet.sol", "addresses": ["0xf05338FeAB6c2416916110930D25bE0AC8D9257f"]}, {"name": "CoreVaultManagerImplementation", "contractName": "CoreVaultManager.sol", "addresses": ["0xE5b27233Baf6C8C8DA18f26DcBC9dC1c00Fc8321"]}, {"name": "CoreVaultManager_FXRP", "contractName": "CoreVaultManagerProxy.sol", "addresses": ["0xD3D56154499D55445c76c1B7f2D22B2E6f755C9B", "0x0CdF65f6de5FFFf9B39252d0296EcE2530770b5a"]}, {"name": "AgentAlwaysAllowedMintersFacet", "contractName": "AgentAlwaysAllowedMintersFacet.sol", "addresses": ["0x20b63a2377b6d3a0C61635bFEBa2E05844857dc2"]}, {"name": "CoreVaultFacet", "contractName": "CoreVaultFacet.sol", "addresses": ["0x86486058B0770289d1583A96CFE31cd9f9A4c2E7", "0x933dDf25424E96E4448f43560e6130A53E8e26fB"]}, {"name": "CoreVaultSettingsFacet", "contractName": "CoreVaultSettingsFacet.sol", "addresses": ["0x4fBd9B0b588c4C2A998915cE493b5E69C1495f59", "0x3Aa5404F6d42879F3449cCB1B5332a48851c41f3"]}]