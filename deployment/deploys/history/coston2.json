[{"name": "testUSDC", "contractName": "FakeERC20.sol", "addresses": ["******************************************"]}, {"name": "testUSDT", "contractName": "FakeERC20.sol", "addresses": ["******************************************"]}, {"name": "testETH", "contractName": "FakeERC20.sol", "addresses": ["******************************************"]}, {"name": "testUSDT0", "contractName": "FakeERC20.sol", "addresses": ["******************************************"]}, {"name": "Price<PERSON><PERSON><PERSON>", "contractName": "FtsoV2PriceStore.sol", "addresses": ["******************************************"]}, {"name": "FtsoV2PriceStore", "contractName": "FtsoV2PriceStore.sol", "addresses": ["******************************************"]}, {"name": "AgentOwnerRegistry", "contractName": "AgentOwnerRegistry.sol", "addresses": ["******************************************"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contractName": "Whitelist.sol", "addresses": ["******************************************"]}, {"name": "AgentVaultProxyImplementation", "contractName": "AgentVault.sol", "addresses": ["******************************************", "******************************************", "0x791FE6C80A7682D9C72787176397BA5c63c7DdB0"]}, {"name": "AgentVaultFactory", "contractName": "AgentVaultFactory.sol", "addresses": ["0x9426C77c843c99079571BeC0ceD8e31b1f49f005", "0xBdc78602E3C74FA40d74c9555C3340946Ea17d8e", "0x1221B2DdA58f7ac682D23b4d37B4fb6b14c565F6"]}, {"name": "CollateralPoolProxyImplementation", "contractName": "CollateralPool.sol", "addresses": ["0x01C2Dae4b2cEbb0ccB6b7Af166D397dEC1513e4E", "0x71621f9611374F4ee6477261Bc71B5cD6069F3d7", "0x2684149b0aF1CB8B02749faf5280C51e18dd4D90", "0x74D0D517d86954897DBa6Ed680FDC583De3EFFDc"]}, {"name": "CollateralPoolFactory", "contractName": "CollateralPoolFactory.sol", "addresses": ["0x3D77acbDF0fB951C4E556C07B4F1b61f8744acfd", "0x359a46C8c56F4A1dD0B7f150C4Fd42Ca28AfA5D6", "0xEc400a6e2D46fC392D05BF94276e6bc1Bab325E1", "0x8e1EDcE257a1dE94c5EedCFfE24803e22f121C61"]}, {"name": "CollateralPoolTokenProxyImplementation", "contractName": "CollateralPoolToken.sol", "addresses": ["0xcA9bFC7D61D72c880C3b9355572Bd8E02A47bE33", "0xd0De5da69397ec71Fd313998d35f04a3B67bf8D2"]}, {"name": "CollateralPoolTokenFactory", "contractName": "CollateralPoolTokenFactory.sol", "addresses": ["0xE6bD1f4a090b61E56279Dec9692FB540734cd9AC", "0x372c26FC82b894FCc1B89f853b2828dD9719C06e"]}, {"name": "AssetManagerControllerImplementation", "contractName": "AssetManagerController.sol", "addresses": ["0x2493371415C6Ef5a62FB234f3523daC70D7ed5Ad", "0x3E6246CB8489DE73E67f5D28733Ec56cDD9644cD", "0xa8e603775996836E22569061517544a33BB385AD", "0xA7A80557F8EbbEF05E4512B4d7e84342F2487069"]}, {"name": "AssetManagerController", "contractName": "AssetManagerControllerProxy.sol", "addresses": ["0x0f575c6eC54176a5681cD5500fc770e9133b8982"]}, {"name": "FAssetImplementation", "contractName": "FAsset.sol", "addresses": ["0xD96006B65a2B5Dc69bBc4B4c31019c3A2A79B50b", "0x2A9d4EBd2196b1f2718400f2ad4Ce716C67C9925", "0x466f987F921aC345Cd9DF4EC3eC8dD2cF9B9dC47"]}, {"name": "AssetManagerInit", "contractName": "AssetManagerInit.sol", "addresses": ["0x874CbbaA09a38965D0bb29D8886a177351D7C323"]}, {"name": "AssetManagerDiamondCutFacet", "contractName": "AssetManagerDiamondCutFacet.sol", "addresses": ["0xfA4Ae4DBE5253745925ec993D923991e6DF587bF", "0xe7CCC6be89aB8b8639b2dFE9A1852ed34B30ee6C"]}, {"name": "DiamondLoupeFacet", "contractName": "DiamondLoupeFacet.sol", "addresses": ["0x4c1c7fA8568e0b365AE64dd44a01C28D21a77854", "0x10372DC30EF18d63B6BAB999456aC0C361D59c62"]}, {"name": "AgentInfoFacet", "contractName": "AgentInfoFacet.sol", "addresses": ["0x9Bb547f21FA85CD9BF3fD130D52d1D42B088c752", "0x2bcd2a62013C9E0bd76cA402fbc087dacb2eB870"]}, {"name": "AvailableAgentsFacet", "contractName": "AvailableAgentsFacet.sol", "addresses": ["0xB995AB8cF44a2d51562A571A5Dc1477702360eEe", "0x7cdbB78ededC8142EB4D7C072C9daBE45d68f800"]}, {"name": "CollateralReservationsFacet", "contractName": "CollateralReservationsFacet.sol", "addresses": ["0x9F7b9e986D07448FEF943a6028a5A88F48e94cB9", "0x83a547cE20D8A6Ac06fc42E557E0Fc104F334460", "0xE9a1c97B35aF15CC67705095973585C2eAB850C4"]}, {"name": "MintingFacet", "contractName": "MintingFacet.sol", "addresses": ["0xe80EF41c69a204ea5C90F033fe722B9D8f5681D8", "0x356aF9c75145324A396B3b80c0849cF7f1240f35"]}, {"name": "RedemptionRequestsFacet", "contractName": "RedemptionRequestsFacet.sol", "addresses": ["0xe6c74f81623Be4Dd580d9DB9aE47a966b3f167F9", "0xE1a61C06f3FCA916F0a362FC7DfC8DA4DB00C01f"]}, {"name": "RedemptionHandshakeFacet", "contractName": "RedemptionHandshakeFacet.sol", "addresses": ["0x2D98960902EA6D0d9188052B23682d32C59d4461"]}, {"name": "RedemptionConfirmationsFacet", "contractName": "RedemptionConfirmationsFacet.sol", "addresses": ["0xf66dEC1236319AA8202A1B0F2aa8442A4ad88A8F", "0xbFb623F2f183adfF3770A7314cf22A7cDF47c849", "0xB496C3a320816AF746a04A6fef1FDB0FA964D35A"]}, {"name": "RedemptionDefaultsFacet", "contractName": "RedemptionDefaultsFacet.sol", "addresses": ["0x5f73833EFDa1054905084232b25384505f2e6cb6", "0xc12E181a073122d320ef0E851664A77b7c633E44"]}, {"name": "LiquidationFacet", "contractName": "LiquidationFacet.sol", "addresses": ["0xc5B0f10BEea6BB5Ac10aF93f494e75904B6fb0cC", "0x996c64A1cc3488076e3bf2F65f98B9239cAf29e8"]}, {"name": "ChallengesFacet", "contractName": "ChallengesFacet.sol", "addresses": ["0x27FEc084D6a09bBB6611063732df1B8BCcba5878", "0x19a5840CaFA5cB2AE400179C155cCF9a304dA860"]}, {"name": "UnderlyingBalanceFacet", "contractName": "UnderlyingBalanceFacet.sol", "addresses": ["0xec3eEe520ec48f5901f71B99feBB109A4df39b2B", "0x556505425B14448B87529C1Ffde519520D6BDA4e"]}, {"name": "UnderlyingTimekeepingFacet", "contractName": "UnderlyingTimekeepingFacet.sol", "addresses": ["0x8b1c69F60F740B3226cC6Df4A814d0EF79f3eA09", "0x4fC6CBD982Bcd415338C6aBA0A2308609Bcec7a4"]}, {"name": "AgentVaultManagementFacet", "contractName": "AgentVaultManagementFacet.sol", "addresses": ["0xfD74D7d5aAd50233329dC60CD126d7afa56DAEe7", "0xb1E94dDf2d51DB9593e4FD4357A34AC901De9135", "0xC0d1Fe3BC10912387330962a8357dB9e10EF3d6F"]}, {"name": "AgentSettingsFacet", "contractName": "AgentSettingsFacet.sol", "addresses": ["0x58C0b2BC2d7a6a79C92495dCcA3cEE2F56147c37", "0x1075A62308dC7Cf92A10b2288b865315a24B2e3d"]}, {"name": "CollateralTypesFacet", "contractName": "CollateralTypesFacet.sol", "addresses": ["0x41D4aD5D841BE7a16A6Dbf9393d848E1E18F9f0b", "0xc42017b25C279C9E1759d45B0eFdBa88953658Ec"]}, {"name": "AgentCollateralFacet", "contractName": "AgentCollateralFacet.sol", "addresses": ["0xe58Fc996afb42621f1C2De0a4E13fd9ffc1F344C", "0x3a10e6f33a795F497477B63d49df04b79770E3b3", "0x83f5375357e92a52096b9A25D9EAEb56bf9B7542"]}, {"name": "SettingsReaderFacet", "contractName": "SettingsReaderFacet.sol", "addresses": ["0xCB871AdD15AE5AA09f6dD5DDCc360E4A4BF06A35", "0x1F5C9EfAF4F7a0FcF1e8a153B318817534614D5B"]}, {"name": "SettingsManagementFacet", "contractName": "SettingsManagementFacet.sol", "addresses": ["0xa7cD5A28Df3ddc66B8fBb86023c3C71E3db91536", "0xd922a60f8c7a5CB25CD1c45FcC4BA7999cA657DB"]}, {"name": "AgentVaultAndPoolSupportFacet", "contractName": "AgentVaultAndPoolSupportFacet.sol", "addresses": ["0x57C2B792a29Ea2Aa0429684aF1202b152f97eAF0", "0x60DAE32F6A960Fb6a0B2CD912ef445b15D538539"]}, {"name": "SystemStateManagementFacet", "contractName": "SystemStateManagementFacet.sol", "addresses": ["0xa54aCb25460d893753e11718Bcc0A1D4959b97aA", "0x1535d5EB6EF02B16b1880Ad8b5B9Cc2AA604f2A4"]}, {"name": "SystemInfoFacet", "contractName": "SystemInfoFacet.sol", "addresses": ["0x76FC49cd9fCF574a33633bba4BBd706792A45A18", "0x01f83CB20576657Fdd1121ac823CA70dcb623bc8"]}, {"name": "EmergencyPauseFacet", "contractName": "EmergencyPauseFacet.sol", "addresses": ["0xB3da34fA657a13A0d8eD35AA611a95e241BB65FC", "0xf0eA8e065F863068A7dB75Bd8b99C43feaca333a"]}, {"name": "EmergencyPauseTransfersFacet", "contractName": "EmergencyPauseTransfersFacet.sol", "addresses": ["0x2FDb3201d240650C0f71a2ec7F2FE0532f1Eb126", "0x9be0370763174E051CCE108C97941239553417FD"]}, {"name": "AgentPingFacet", "contractName": "AgentPingFacet.sol", "addresses": ["0x16CbB3e305277128bFb75cd276C4C844f3503482", "0x6C8A40D7bB4d73ECDf98Ec9abe0fECE13cd01bF6"]}, {"name": "AgentAlwaysAllowedMintersFacet", "contractName": "AgentAlwaysAllowedMintersFacet.sol", "addresses": ["0xEbc8bF326a2a6Be6cB2673bFDa37E81Eb3ed3bB2", "0x190BEAe37951004A03AcbF5BF10d2CCE955AA470"]}, {"name": "RedemptionTimeExtensionFacet", "contractName": "RedemptionTimeExtensionFacet.sol", "addresses": ["0x81db2BA036BE2b3e1c79a09F3b509e046AAfa96d", "0xAb03230C9a374E953cD739d79D6e1CD64b75B4D8"]}, {"name": "TransferFeeFacet", "contractName": "TransferFeeFacet.sol", "addresses": ["0xe8333724D7B0BBa9d79C4e7a7100E79475169076"]}, {"name": "CoreVaultFacet", "contractName": "CoreVaultFacet.sol", "addresses": ["0x349068D4145e0d0E2F6173e26C9CAaC0803BB856"]}, {"name": "CoreVaultSettingsFacet", "contractName": "CoreVaultSettingsFacet.sol", "addresses": ["0xF8e94262c2B56589B123AcCAB021F3aC4964dC7b", "0xaBE05b617899A3D8c36A60a03a4B9fA995f4762E"]}, {"name": "AssetManager_FTestXRP", "contractName": "AssetManager.sol", "addresses": ["0xDeD50DA9C3492Bee44560a4B35cFe0e778F41eC5"]}, {"name": "FTestXRP", "contractName": "FAssetProxy.sol", "addresses": ["0x8b4abA9C4BD7DD961659b02129beE20c6286e17F"]}, {"name": "CoreVaultManagerImplementation", "contractName": "CoreVaultManager.sol", "addresses": ["0x76298183C2D4Ba0602348828Ff1b7Ee33c6B6c05"]}, {"name": "CoreVaultManager_FTestXRP", "contractName": "CoreVaultManagerProxy.sol", "addresses": ["0x11De168Bfe62425Af06cc82f3b19180DaB1e4E31"]}, {"name": "CoreVaultClientFacet", "contractName": "CoreVaultClientFacet.sol", "addresses": ["0xe7A7495b1937F73e9Fb7c7F98b1506a819b49Ed1"]}, {"name": "CoreVaultClientSettingsFacet", "contractName": "CoreVaultClientSettingsFacet.sol", "addresses": ["0x53129D43bDa547E73a6Fc37B969F915811960028"]}, {"name": "MintingDefaultsFacet", "contractName": "MintingDefaultsFacet.sol", "addresses": ["0xA8446EAee75725dffC002Ba55b22aA8Ba1823F7e"]}]