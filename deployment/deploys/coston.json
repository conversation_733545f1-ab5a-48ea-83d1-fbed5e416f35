[{"name": "InflationAllocation", "contractName": "InflationAllocation.sol", "address": "0xD11CB940C8375c119CAe7b8A75844ec35371F2E0"}, {"name": "<PERSON><PERSON>", "contractName": "Relay.sol", "address": "0x92a6E1127262106611e1e129BB64B6D8654273F7"}, {"name": "FdcHub", "contractName": "FdcHub.sol", "address": "0x1c78A073E3BD2aCa4cc327d55FB0cD4f0549B55b"}, {"name": "FlareDaemon", "contractName": "FlareDaemon.sol", "address": "0x1000000000000000000000000000000000000002"}, {"name": "PriceSubmitter", "contractName": "PriceSubmitter.sol", "address": "0x1000000000000000000000000000000000000003"}, {"name": "Inflation", "contractName": "Inflation.sol", "address": "0x12d20f1b378cd8ee8dA4729262645EC62FD52307"}, {"name": "Supply", "contractName": "Supply.sol", "address": "0x98D2B1835A8D4a7c9915Ca212bDb5AA259642fac"}, {"name": "FtsoRewardManager", "contractName": "FtsoRewardManager.sol", "address": "0xfD36176C63dA52E783a347DE3544B0b44C7054a6"}, {"name": "CleanupBlockNumberManager", "contractName": "CleanupBlockNumberManager.sol", "address": "0xB50DAcdaA3Af02F8A0533902C117ADFC31A31Ccf"}, {"name": "FtsoRegistry", "contractName": "FtsoRegistry.sol", "address": "0xf7Bbf40145C82Fca13011C783AaeCa6bD95fd652"}, {"name": "VoterWhitelister", "contractName": "VoterWhitelister.sol", "address": "0xFAe0fd738dAbc8a0426F47437322b6d026A9FD95"}, {"name": "FtsoManager", "contractName": "FtsoManager.sol", "address": "0x12B6E9dB4Ac9889aBb92beAA6CF7d71f334c1168"}, {"name": "WNat", "contractName": "WNat.sol", "address": "0x767b25A658E8FC8ab6eBbd52043495dB61b4ea91"}, {"name": "WCFLR", "contractName": "WNat.sol", "address": "0x767b25A658E8FC8ab6eBbd52043495dB61b4ea91"}, {"name": "FtsoWnat", "contractName": "Ftso.sol", "address": "0xaD256562ed63ABD503935CbEE276a72160d200e8"}, {"name": "FtsoTestXrp", "contractName": "Ftso.sol", "address": "0x655bC84c44447d8A6087300dEd38eEcc125721B8"}, {"name": "FtsoTestLtc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestXlm", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestDoge", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAda", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAlgo", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestBtc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestEth", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestFil", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestArb", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestAvax", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestBnb", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestMatic", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestSol", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestUsdc", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestUsdt", "contractName": "Ftso.sol", "address": "******************************************"}, {"name": "FtsoTestXdc", "contractName": "Ftso.sol", "address": "0x109007f065EcEF4994bCB67a1891764F949088fe"}, {"name": "GovernanceSettings", "contractName": "GovernanceSettings.sol", "address": "0x183fAfF6997C98A812A3B98748Fc80241D08f312"}, {"name": "GovernanceVotePower", "contractName": "GovernanceVotePower.sol", "address": "0xEb10E3a19Ae0e2564E2c86dBd89517fe7F24789d"}, {"name": "PollingFoundation", "contractName": "PollingFoundation.sol", "address": "0xe435D00D30DEc987Afd5dAC2205F2b20e2F90eB8"}, {"name": "AddressUpdater", "contractName": "AddressUpdater.sol", "address": "0xb006c0cb0b2b8ae48E5B12883Cd5a13654cc6e20"}, {"name": "FlareContractRegistry", "contractName": "FlareContractRegistry.sol", "address": "0xaD67FE66660Fb8dFE9d6b1b4240d8650e30F6019"}, {"name": "PollingFtso", "contractName": "PollingFtso.sol", "address": "0xD036a8F254ef782cb93af4F829A1568E992c3864"}, {"name": "ClaimSetupManager", "contractName": "ClaimSetupManager.sol", "address": "0x3F55A3c8012C26B140AcC23d061E7c4626d64e01"}, {"name": "DelegationAccount", "contractName": "DelegationAccount.sol", "address": "0xE2Ee339b2608Fe20ca2f721e938a640e0E5ee546"}, {"name": "Flare<PERSON>set<PERSON>ry", "contractName": "FlareAssetRegistry.sol", "address": "******************************************"}, {"name": "WNatRegistryProvider", "contractName": "WNatRegistryProvider.sol", "address": "******************************************"}, {"name": "testUSDC", "contractName": "FakeERC20.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "testUSDT", "contractName": "FakeERC20.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "testETH", "contractName": "FakeERC20.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FdcVerification", "contractName": "FdcVerification.sol", "address": "******************************************"}, {"name": "Price<PERSON><PERSON><PERSON>", "contractName": "FtsoV2PriceStore.sol", "address": "******************************************"}, {"name": "FtsoV2PriceStore", "contractName": "FtsoV2PriceStore.sol", "address": "******************************************"}, {"name": "FakePriceReader", "contractName": "FakePriceReader.sol", "address": "******************************************"}, {"name": "AgentOwnerRegistry", "contractName": "AgentOwnerRegistry.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "AgentVaultProxyImplementation", "contractName": "AgentVault.sol", "address": "******************************************"}, {"name": "CollateralPoolProxyImplementation", "contractName": "CollateralPool.sol", "address": "0x06f3aC31D65E841Db49980d0b89A4a12c215F569"}, {"name": "CollateralPoolTokenProxyImplementation", "contractName": "CollateralPoolToken.sol", "address": "0x5db55f75B299584437FA58539897de82b1755e4E"}, {"name": "AgentVaultFactory", "contractName": "AgentVaultFactory.sol", "address": "0xe2F9529e00ebCb32118479968D5F077c3982570F"}, {"name": "CollateralPoolFactory", "contractName": "CollateralPoolFactory.sol", "address": "0xe57B523bF4AF5b54A561FE1Cf8A026b8049A3C8d"}, {"name": "CollateralPoolTokenFactory", "contractName": "CollateralPoolTokenFactory.sol", "address": "0x45fE37c493eA419B01AB5A1008D5Eb0470F8A054"}, {"name": "AssetManagerControllerImplementation", "contractName": "AssetManagerController.sol", "address": "0x4E031ba72532aa28831EBC92c198ab9b56233f97"}, {"name": "AssetManagerController", "contractName": "AssetManagerControllerProxy.sol", "address": "0x572DeF121DC83332887E25e34aD51C2f5f40BC97", "mustSwitchToProduction": true}, {"name": "FAssetImplementation", "contractName": "FAsset.sol", "address": "0x3ad3158894ba06439F791A5018C61E44e2Efb0E0"}, {"name": "AssetManager_FTestXRP", "contractName": "AssetManager.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FTestXRP", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManager_FSimCoinX", "contractName": "AssetManager.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FSimCoinX", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManager_FTestBTC", "contractName": "AssetManager.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FTestBTC", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManager_FTestDOGE", "contractName": "AssetManager.sol", "address": "******************************************", "mustSwitchToProduction": true}, {"name": "FTestDOGE", "contractName": "FAssetProxy.sol", "address": "******************************************"}, {"name": "AssetManagerInit", "contractName": "AssetManagerInit.sol", "address": "******************************************"}, {"name": "AssetManagerDiamondCutFacet", "contractName": "AssetManagerDiamondCutFacet.sol", "address": "******************************************"}, {"name": "DiamondLoupeFacet", "contractName": "DiamondLoupeFacet.sol", "address": "******************************************"}, {"name": "AgentInfoFacet", "contractName": "AgentInfoFacet.sol", "address": "******************************************"}, {"name": "AvailableAgentsFacet", "contractName": "AvailableAgentsFacet.sol", "address": "0xf3f77777FA1C0c3a419a1831DEe1b1c45DB21c76"}, {"name": "CollateralReservationsFacet", "contractName": "CollateralReservationsFacet.sol", "address": "0xc88Fd2c95853f186758137189B28467602A87efc"}, {"name": "MintingFacet", "contractName": "MintingFacet.sol", "address": "0x0157c718d5c728cE7a4C197607a47834f08fa4a8"}, {"name": "RedemptionRequestsFacet", "contractName": "RedemptionRequestsFacet.sol", "address": "0x0175Cc6494c1518E0c069a601A7f96AEC2B07143"}, {"name": "RedemptionConfirmationsFacet", "contractName": "RedemptionConfirmationsFacet.sol", "address": "0x07133f477fd33d3026c66b7C74F7eCeaBaf80da2"}, {"name": "RedemptionDefaultsFacet", "contractName": "RedemptionDefaultsFacet.sol", "address": "0x7FE2cA07535332A1d0635a758Df299B421dC865e"}, {"name": "LiquidationFacet", "contractName": "LiquidationFacet.sol", "address": "0x6FA5A79bB070Cc18508E5de1a9723E5f1F458d6f"}, {"name": "ChallengesFacet", "contractName": "ChallengesFacet.sol", "address": "0x276D0d4f50D04330b7D34B8BF37707D984071e8C"}, {"name": "UnderlyingBalanceFacet", "contractName": "UnderlyingBalanceFacet.sol", "address": "0x6257B30C5f9f3E9bE3a214595743Ce9D1CBBE593"}, {"name": "UnderlyingTimekeepingFacet", "contractName": "UnderlyingTimekeepingFacet.sol", "address": "0x136a85D668d0C691687D29B68dfAAC456F402e88"}, {"name": "AgentVaultManagementFacet", "contractName": "AgentVaultManagementFacet.sol", "address": "0x1B1233c91639978d2bAE7801133711d628103995"}, {"name": "AgentSettingsFacet", "contractName": "AgentSettingsFacet.sol", "address": "0xE3884E7dDDC7B84F4446520b89fCec0D9BeAb9e2"}, {"name": "CollateralTypesFacet", "contractName": "CollateralTypesFacet.sol", "address": "0x762127d7b3350402F21b5e9c78DA96975D13Ef22"}, {"name": "AgentCollateralFacet", "contractName": "AgentCollateralFacet.sol", "address": "0xABeD16753793E627c473A3FD3e0e8f708E75dc17"}, {"name": "SettingsReaderFacet", "contractName": "SettingsReaderFacet.sol", "address": "0xE091955cAef3ee552e62F8EFB6374448cAf6885D"}, {"name": "SettingsManagementFacet", "contractName": "SettingsManagementFacet.sol", "address": "0x65752137B4960B84091a7bD5AF7CDA69587c67F7"}, {"name": "AgentVaultAndPoolSupportFacet", "contractName": "AgentVaultAndPoolSupportFacet.sol", "address": "0xaf1f81e13B5105b062C969797B03aD0FE491BE31"}, {"name": "SystemStateManagementFacet", "contractName": "SystemStateManagementFacet.sol", "address": "0x0AFFd6c07dba9E9798Ec6084e05fe35Dd633508B"}, {"name": "SystemInfoFacet", "contractName": "SystemInfoFacet.sol", "address": "0x5D6F34ADEd69DB39a27b5BA23735B3375Cca8d5a"}, {"name": "EmergencyPauseFacet", "contractName": "EmergencyPauseFacet.sol", "address": "0x858D9c090B962F03a1Da23169AFeD40392e785bb"}, {"name": "AgentPingFacet", "contractName": "AgentPingFacet.sol", "address": "0x40890974F7757aedc53eE7afC072F904a57EB8da"}, {"name": "RedemptionTimeExtensionFacet", "contractName": "RedemptionTimeExtensionFacet.sol", "address": "0x620E9345144F0338Af0A384ABc3409543c903328"}, {"name": "EmergencyPauseTransfersFacet", "contractName": "EmergencyPauseTransfersFacet.sol", "address": "0xa44D8C26B3aD6ffe4F2D0f1ed3bf84e1110442D1"}, {"name": "CoreVaultManagerImplementation", "contractName": "CoreVaultManager.sol", "address": "0xaC8E162224e73320eE83fA885Eccdf863eC7a532"}, {"name": "CoreVaultManager_FTestXRP", "contractName": "CoreVaultManagerProxy.sol", "address": "0xCC9206d850fb9140019E78e6b2B309193a34DA3c", "mustSwitchToProduction": true}, {"name": "AgentAlwaysAllowedMintersFacet", "contractName": "AgentAlwaysAllowedMintersFacet.sol", "address": "0xe248D95D7CE31333F165e2F8C99570934832551D"}, {"name": "CoreVaultClientFacet", "contractName": "CoreVaultClientFacet.sol", "address": "0x4c5d826714Dc2606E9fbe69E268E0b7D8372D8b5"}, {"name": "CoreVaultClientSettingsFacet", "contractName": "CoreVaultClientSettingsFacet.sol", "address": "0xD8efe757Fb1C93C7327dC114Fd2BB115f8F2281C"}, {"name": "MintingDefaultsFacet", "contractName": "MintingDefaultsFacet.sol", "address": "0x0441873365Bf6E3FEFB51DA3F77A04D1aaf87D8c"}]