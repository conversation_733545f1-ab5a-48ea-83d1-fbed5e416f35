{"$schema": "./diamond-cuts.schema.json", "diamond": ["AssetManager_FTestXRP"], "facets": [{"contract": "SettingsManagementFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentVaultManagementFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentSettingsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CollateralReservationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CollateralTypesFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "MintingFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionRequestsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "SettingsManagementFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CoreVaultFacet", "exposedInterfaces": ["ICoreVault"]}]}