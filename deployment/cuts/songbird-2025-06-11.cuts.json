{"$schema": "./diamond-cuts.schema.json", "diamond": ["AssetManager_FXRP", "AssetManager_FBTC", "AssetManager_FDOGE"], "facets": [{"contract": "CoreVaultFacet", "exposedInterfaces": ["ICoreVault"]}, {"contract": "CoreVaultSettingsFacet", "exposedInterfaces": ["ICoreVaultSettings"]}, {"contract": "AgentCollateralFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentSettingsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "AgentVaultManagementFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "ChallengesFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CollateralReservationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "CollateralTypesFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "MintingFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionConfirmationsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionDefaultsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionHandshakeFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "RedemptionRequestsFacet", "exposedInterfaces": ["IIAssetManager"]}, {"contract": "SettingsManagementFacet", "exposedInterfaces": ["IIAssetManager"]}]}