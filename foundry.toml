[profile.default]
src = "contracts"
out = "artifacts-forge"
libs = ["node_modules", "lib"]
test = "test-forge"
cache_path = 'cache-forge'
evm_version = 'london'
fs_permissions = [{ access = "read", path = "./artifacts-forge/"}]
optimizer = true
optimizer_runs = 200
ffi = true
remappings = [
    "forge-std/=lib/forge-std/src/"
]

[invariant]
show_metrics = true
fail_on_revert = false
runs = 100 # default is 256
depth = 200 # default is 500

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options