## Immunefi FAssets Audit Competition Scope
| Smart Contract | nSLOC
| :--- | ---:
| [contracts/assetManager/facets/AgentAlwaysAllowedMintersFacet.sol](/contracts/assetManager/facets/AgentAlwaysAllowedMintersFacet.sol) | 35
| [contracts/assetManager/facets/AgentCollateralFacet.sol](/contracts/assetManager/facets/AgentCollateralFacet.sol) | 63
| [contracts/assetManager/facets/AgentInfoFacet.sol](/contracts/assetManager/facets/AgentInfoFacet.sol) | 82
| [contracts/assetManager/facets/AgentPingFacet.sol](/contracts/assetManager/facets/AgentPingFacet.sol) | 16
| [contracts/assetManager/facets/AgentSettingsFacet.sol](/contracts/assetManager/facets/AgentSettingsFacet.sol) | 32
| [contracts/assetManager/facets/AgentVaultAndPoolSupportFacet.sol](/contracts/assetManager/facets/AgentVaultAndPoolSupportFacet.sol) | 39
| [contracts/assetManager/facets/AgentVaultManagementFacet.sol](/contracts/assetManager/facets/AgentVaultManagementFacet.sol) | 46
| [contracts/assetManager/facets/AssetManagerBase.sol](/contracts/assetManager/facets/AssetManagerBase.sol) | 44
| [contracts/assetManager/facets/AssetManagerDiamondCutFacet.sol](/contracts/assetManager/facets/AssetManagerDiamondCutFacet.sol) | 17
| [contracts/assetManager/facets/AssetManagerInit.sol](/contracts/assetManager/facets/AssetManagerInit.sol) | 45
| [contracts/assetManager/facets/AvailableAgentsFacet.sol](/contracts/assetManager/facets/AvailableAgentsFacet.sol) | 45
| [contracts/assetManager/facets/ChallengesFacet.sol](/contracts/assetManager/facets/ChallengesFacet.sol) | 37
| [contracts/assetManager/facets/CollateralReservationsFacet.sol](/contracts/assetManager/facets/CollateralReservationsFacet.sol) | 74
| [contracts/assetManager/facets/CollateralTypesFacet.sol](/contracts/assetManager/facets/CollateralTypesFacet.sol) | 51
| [contracts/assetManager/facets/CoreVaultFacet.sol](/contracts/assetManager/facets/CoreVaultFacet.sol) | 96
| [contracts/assetManager/facets/CoreVaultSettingsFacet.sol](/contracts/assetManager/facets/CoreVaultSettingsFacet.sol) | 172
| [contracts/assetManager/facets/EmergencyPauseFacet.sol](/contracts/assetManager/facets/EmergencyPauseFacet.sol) | 73
| [contracts/assetManager/facets/EmergencyPauseTransfersFacet.sol](/contracts/assetManager/facets/EmergencyPauseTransfersFacet.sol) | 75
| [contracts/assetManager/facets/LiquidationFacet.sol](/contracts/assetManager/facets/LiquidationFacet.sol) | 39
| [contracts/assetManager/facets/MintingFacet.sol](/contracts/assetManager/facets/MintingFacet.sol) | 38
| [contracts/assetManager/facets/RedemptionConfirmationsFacet.sol](/contracts/assetManager/facets/RedemptionConfirmationsFacet.sol) | 17
| [contracts/assetManager/facets/RedemptionDefaultsFacet.sol](/contracts/assetManager/facets/RedemptionDefaultsFacet.sol) | 34
| [contracts/assetManager/facets/RedemptionRequestsFacet.sol](/contracts/assetManager/facets/RedemptionRequestsFacet.sol) | 82
| [contracts/assetManager/facets/RedemptionTimeExtensionFacet.sol](/contracts/assetManager/facets/RedemptionTimeExtensionFacet.sol) | 42
| [contracts/assetManager/facets/SettingsManagementFacet.sol](/contracts/assetManager/facets/SettingsManagementFacet.sol) | 585
| [contracts/assetManager/facets/SettingsReaderFacet.sol](/contracts/assetManager/facets/SettingsReaderFacet.sol) | 56
| [contracts/assetManager/facets/SystemInfoFacet.sol](/contracts/assetManager/facets/SystemInfoFacet.sol) | 106
| [contracts/assetManager/facets/SystemStateManagementFacet.sol](/contracts/assetManager/facets/SystemStateManagementFacet.sol) | 30
| [contracts/assetManager/facets/UnderlyingBalanceFacet.sol](/contracts/assetManager/facets/UnderlyingBalanceFacet.sol) | 38
| [contracts/assetManager/facets/UnderlyingTimekeepingFacet.sol](/contracts/assetManager/facets/UnderlyingTimekeepingFacet.sol) | 23
| [contracts/assetManager/implementation/AgentOwnerRegistry.sol](/contracts/assetManager/implementation/AgentOwnerRegistry.sol) | 137
| [contracts/assetManager/implementation/AgentVault.sol](/contracts/assetManager/implementation/AgentVault.sol) | 223
| [contracts/assetManager/implementation/AgentVaultFactory.sol](/contracts/assetManager/implementation/AgentVaultFactory.sol) | 27
| [contracts/assetManager/implementation/AssetManager.sol](/contracts/assetManager/implementation/AssetManager.sol) | 9
| [contracts/assetManager/implementation/AssetManagerController.sol](/contracts/assetManager/implementation/AssetManagerController.sol) | 629
| [contracts/assetManager/implementation/AssetManagerControllerProxy.sol](/contracts/assetManager/implementation/AssetManagerControllerProxy.sol) | 20
| [contracts/assetManager/implementation/CollateralPool.sol](/contracts/assetManager/implementation/CollateralPool.sol) | 744
| [contracts/assetManager/implementation/CollateralPoolFactory.sol](/contracts/assetManager/implementation/CollateralPoolFactory.sol) | 40
| [contracts/assetManager/implementation/CollateralPoolToken.sol](/contracts/assetManager/implementation/CollateralPoolToken.sol) | 217
| [contracts/assetManager/implementation/CollateralPoolTokenFactory.sol](/contracts/assetManager/implementation/CollateralPoolTokenFactory.sol) | 34
| [contracts/assetManager/implementation/CoreVaultManager.sol](/contracts/assetManager/implementation/CoreVaultManager.sol) | 620
| [contracts/assetManager/implementation/CoreVaultManagerProxy.sol](/contracts/assetManager/implementation/CoreVaultManagerProxy.sol) | 26
| [contracts/assetManager/implementation/FtsoV2PriceStore.sol](/contracts/assetManager/implementation/FtsoV2PriceStore.sol) | 285
| [contracts/assetManager/interfaces/IIAgentVaultFactory.sol](/contracts/assetManager/interfaces/IIAgentVaultFactory.sol) | 7
| [contracts/assetManager/interfaces/IICollateralPoolFactory.sol](/contracts/assetManager/interfaces/IICollateralPoolFactory.sol) | 12
| [contracts/assetManager/interfaces/IICollateralPoolTokenFactory.sol](/contracts/assetManager/interfaces/IICollateralPoolTokenFactory.sol) | 11
| [contracts/assetManager/../agentVault/interfaces/IIAgentVault.sol](/contracts/assetManager/../agentVault/interfaces/IIAgentVault.sol) | 13
| [contracts/assetManager/interfaces/IIAssetManager.sol](/contracts/assetManager/interfaces/IIAssetManager.sol) | 82
| [contracts/assetManager/interfaces/IIAssetManagerController.sol](/contracts/assetManager/interfaces/IIAssetManagerController.sol) | 6
| [contracts/assetManager/interfaces/IICollateralPool.sol](/contracts/assetManager/interfaces/IICollateralPool.sol) | 21
| [contracts/assetManager/interfaces/IICollateralPoolToken.sol](/contracts/assetManager/interfaces/IICollateralPoolToken.sol) | 9
| [contracts/assetManager/interfaces/IICoreVaultManager.sol](/contracts/assetManager/interfaces/IICoreVaultManager.sol) | 16
| [contracts/assetManager/interfaces/IISettingsManagement.sol](/contracts/assetManager/interfaces/IISettingsManagement.sol) | 101
| [contracts/assetManager/interfaces/IPriceChangeEmitter.sol](/contracts/assetManager/interfaces/IPriceChangeEmitter.sol) | 5
| [contracts/assetManager/interfaces/IPricePublisher.sol](/contracts/assetManager/interfaces/IPricePublisher.sol) | 26
| [contracts/assetManager/interfaces/IPriceReader.sol](/contracts/assetManager/interfaces/IPriceReader.sol) | 12
| [contracts/assetManager/interfaces/IUpgradableContractFactory.sol](/contracts/assetManager/interfaces/IUpgradableContractFactory.sol) | 5
| [contracts/assetManager/interfaces/IWNat.sol](/contracts/assetManager/interfaces/IWNat.sol) | 8
| [contracts/assetManager/library/AgentCollateral.sol](/contracts/assetManager/library/AgentCollateral.sol) | 225
| [contracts/assetManager/library/Agents.sol](/contracts/assetManager/library/Agents.sol) | 460
| [contracts/assetManager/library/CollateralReservations.sol](/contracts/assetManager/library/CollateralReservations.sol) | 333
| [contracts/assetManager/library/CollateralTypes.sol](/contracts/assetManager/library/CollateralTypes.sol) | 188
| [contracts/assetManager/library/Conversion.sol](/contracts/assetManager/library/Conversion.sol) | 163
| [contracts/assetManager/library/CoreVault.sol](/contracts/assetManager/library/CoreVault.sol) | 282
| [contracts/assetManager/library/Globals.sol](/contracts/assetManager/library/Globals.sol) | 46
| [contracts/assetManager/library/Liquidation.sol](/contracts/assetManager/library/Liquidation.sol) | 372
| [contracts/assetManager/library/LiquidationPaymentStrategy.sol](/contracts/assetManager/library/LiquidationPaymentStrategy.sol) | 51
| [contracts/assetManager/library/MerkleTree.sol](/contracts/assetManager/library/MerkleTree.sol) | 73
| [contracts/assetManager/library/Minting.sol](/contracts/assetManager/library/Minting.sol) | 187
| [contracts/assetManager/library/RedemptionConfirmations.sol](/contracts/assetManager/library/RedemptionConfirmations.sol) | 118
| [contracts/assetManager/library/RedemptionFailures.sol](/contracts/assetManager/library/RedemptionFailures.sol) | 182
| [contracts/assetManager/library/RedemptionQueueInfo.sol](/contracts/assetManager/library/RedemptionQueueInfo.sol) | 49
| [contracts/assetManager/library/RedemptionRequests.sol](/contracts/assetManager/library/RedemptionRequests.sol) | 342
| [contracts/assetManager/library/Redemptions.sol](/contracts/assetManager/library/Redemptions.sol) | 146
| [contracts/assetManager/library/SettingsInitializer.sol](/contracts/assetManager/library/SettingsInitializer.sol) | 87
| [contracts/assetManager/library/SettingsUpdater.sol](/contracts/assetManager/library/SettingsUpdater.sol) | 23
| [contracts/assetManager/library/SettingsValidators.sol](/contracts/assetManager/library/SettingsValidators.sol) | 31
| [contracts/assetManager/library/StateUpdater.sol](/contracts/assetManager/library/StateUpdater.sol) | 62
| [contracts/assetManager/library/TransactionAttestation.sol](/contracts/assetManager/library/TransactionAttestation.sol) | 74
| [contracts/assetManager/library/UnderlyingBalance.sol](/contracts/assetManager/library/UnderlyingBalance.sol) | 72
| [contracts/assetManager/library/data/Agent.sol](/contracts/assetManager/library/data/Agent.sol) | 101
| [contracts/assetManager/library/data/AssetManagerState.sol](/contracts/assetManager/library/data/AssetManagerState.sol) | 46
| [contracts/assetManager/library/data/Collateral.sol](/contracts/assetManager/library/data/Collateral.sol) | 18
| [contracts/assetManager/library/data/CollateralReservation.sol](/contracts/assetManager/library/data/CollateralReservation.sol) | 18
| [contracts/assetManager/library/data/CollateralTypeInt.sol](/contracts/assetManager/library/data/CollateralTypeInt.sol) | 17
| [contracts/assetManager/library/data/PaymentConfirmations.sol](/contracts/assetManager/library/data/PaymentConfirmations.sol) | 61
| [contracts/assetManager/library/data/PaymentReference.sol](/contracts/assetManager/library/data/PaymentReference.sol) | 50
| [contracts/assetManager/library/data/Redemption.sol](/contracts/assetManager/library/data/Redemption.sol) | 29
| [contracts/assetManager/library/data/RedemptionQueue.sol](/contracts/assetManager/library/data/RedemptionQueue.sol) | 100
| [contracts/assetManager/library/data/RedemptionTimeExtension.sol](/contracts/assetManager/library/data/RedemptionTimeExtension.sol) | 48
| [contracts/assetManager/library/data/UnderlyingAddressOwnership.sol](/contracts/assetManager/library/data/UnderlyingAddressOwnership.sol) | 60
| [contracts/diamond/facets/DiamondLoupeFacet.sol](/contracts/diamond/facets/DiamondLoupeFacet.sol) | 111
| [contracts/diamond/implementation/Diamond.sol](/contracts/diamond/implementation/Diamond.sol) | 29
| [contracts/diamond/interfaces/IDiamond.sol](/contracts/diamond/interfaces/IDiamond.sol) | 10
| [contracts/diamond/interfaces/IDiamondCut.sol](/contracts/diamond/interfaces/IDiamondCut.sol) | 9
| [contracts/diamond/interfaces/IDiamondLoupe.sol](/contracts/diamond/interfaces/IDiamondLoupe.sol) | 11
| [contracts/diamond/library/LibDiamond.sol](/contracts/diamond/library/LibDiamond.sol) | 155
| [contracts/fassetToken/implementation/CheckPointable.sol](/contracts/fassetToken/implementation/CheckPointable.sol) | 83
| [contracts/fassetToken/implementation/FAsset.sol](/contracts/fassetToken/implementation/FAsset.sol) | 242
| [contracts/fassetToken/implementation/FAssetProxy.sol](/contracts/fassetToken/implementation/FAssetProxy.sol) | 18
| [contracts/fassetToken/interfaces/IICheckPointable.sol](/contracts/fassetToken/interfaces/IICheckPointable.sol) | 5
| [contracts/fassetToken/interfaces/IIFAsset.sol](/contracts/fassetToken/interfaces/IIFAsset.sol) | 12
| [contracts/fassetToken/library/CheckPointHistory.sol](/contracts/fassetToken/library/CheckPointHistory.sol) | 114
| [contracts/fassetToken/library/CheckPointsByAddress.sol](/contracts/fassetToken/library/CheckPointsByAddress.sol) | 68
| [contracts/flareSmartContracts/implementation/AddressUpdatable.sol](/contracts/flareSmartContracts/implementation/AddressUpdatable.sol) | 58
| [contracts/governance/implementation/Governed.sol](/contracts/governance/implementation/Governed.sol) | 8
| [contracts/governance/implementation/GovernedBase.sol](/contracts/governance/implementation/GovernedBase.sol) | 133
| [contracts/governance/implementation/GovernedProxyImplementation.sol](/contracts/governance/implementation/GovernedProxyImplementation.sol) | 8
| [contracts/governance/interfaces/IGoverned.sol](/contracts/governance/interfaces/IGoverned.sol) | 16
| [contracts/openzeppelin/library/Reentrancy.sol](/contracts/openzeppelin/library/Reentrancy.sol) | 34
| [contracts/openzeppelin/security/ReentrancyGuard.sol](/contracts/openzeppelin/security/ReentrancyGuard.sol) | 15
| [contracts/openzeppelin/token/ERC20Permit.sol](/contracts/openzeppelin/token/ERC20Permit.sol) | 52
| [contracts/openzeppelin/utils/EIP712.sol](/contracts/openzeppelin/utils/EIP712.sol) | 65
| [contracts/userInterfaces/IAgentAlwaysAllowedMinters.sol](/contracts/userInterfaces/IAgentAlwaysAllowedMinters.sol) | 10
| [contracts/userInterfaces/IAgentOwnerRegistry.sol](/contracts/userInterfaces/IAgentOwnerRegistry.sol) | 33
| [contracts/userInterfaces/IAgentPing.sol](/contracts/userInterfaces/IAgentPing.sol) | 21
| [contracts/userInterfaces/IAgentVault.sol](/contracts/userInterfaces/IAgentVault.sol) | 40
| [contracts/userInterfaces/IAssetManager.sol](/contracts/userInterfaces/IAssetManager.sol) | 327
| [contracts/userInterfaces/IAssetManagerEvents.sol](/contracts/userInterfaces/IAssetManagerEvents.sol) | 284
| [contracts/userInterfaces/ICollateralPool.sol](/contracts/userInterfaces/ICollateralPool.sol) | 110
| [contracts/userInterfaces/ICollateralPoolToken.sol](/contracts/userInterfaces/ICollateralPoolToken.sol) | 26
| [contracts/userInterfaces/ICoreVault.sol](/contracts/userInterfaces/ICoreVault.sol) | 56
| [contracts/userInterfaces/ICoreVaultManager.sol](/contracts/userInterfaces/ICoreVaultManager.sol) | 127
| [contracts/userInterfaces/ICoreVaultSettings.sol](/contracts/userInterfaces/ICoreVaultSettings.sol) | 38
| [contracts/userInterfaces/IFAsset.sol](/contracts/userInterfaces/IFAsset.sol) | 21
| [contracts/userInterfaces/IRedemptionTimeExtension.sol](/contracts/userInterfaces/IRedemptionTimeExtension.sol) | 8
| [contracts/userInterfaces/data/AgentInfo.sol](/contracts/userInterfaces/data/AgentInfo.sol) | 56
| [contracts/userInterfaces/data/AgentSettings.sol](/contracts/userInterfaces/data/AgentSettings.sol) | 17
| [contracts/userInterfaces/data/AssetManagerSettings.sol](/contracts/userInterfaces/data/AssetManagerSettings.sol) | 65
| [contracts/userInterfaces/data/AvailableAgentInfo.sol](/contracts/userInterfaces/data/AvailableAgentInfo.sol) | 13
| [contracts/userInterfaces/data/CollateralReservationInfo.sol](/contracts/userInterfaces/data/CollateralReservationInfo.sol) | 21
| [contracts/userInterfaces/data/CollateralType.sol](/contracts/userInterfaces/data/CollateralType.sol) | 21
| [contracts/userInterfaces/data/RedemptionRequestInfo.sol](/contracts/userInterfaces/data/RedemptionRequestInfo.sol) | 28
| [contracts/userInterfaces/data/RedemptionTicketInfo.sol](/contracts/userInterfaces/data/RedemptionTicketInfo.sol) | 8
| [contracts/utils/interfaces/IUpgradableProxy.sol](/contracts/utils/interfaces/IUpgradableProxy.sol) | 7
| [contracts/utils/library/MathUtils.sol](/contracts/utils/library/MathUtils.sol) | 10
| [contracts/utils/library/SafeMath64.sol](/contracts/utils/library/SafeMath64.sol) | 27
| [contracts/utils/library/SafePct.sol](/contracts/utils/library/SafePct.sol) | 29
| [contracts/utils/library/Transfers.sol](/contracts/utils/library/Transfers.sol) | 29
