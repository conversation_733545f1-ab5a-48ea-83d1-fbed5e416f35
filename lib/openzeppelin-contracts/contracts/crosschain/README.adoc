= Cross Chain Awareness

[.readme-notice]
NOTE: This document is better viewed at https://docs.openzeppelin.com/contracts/api/crosschain

This directory provides building blocks to improve cross-chain awareness of smart contracts.

- {CrossChainEnabled} is an abstraction that contains accessors and modifiers to control the execution flow when receiving cross-chain messages.

== CrossChainEnabled specializations

The following specializations of {CrossChainEnabled} provide implementations of the {CrossChainEnabled} abstraction for specific bridges. This can be used to complex cross-chain aware components such as {AccessControlCrossChain}.

{{CrossChainEnabledAMB}}

{{CrossChainEnabledArbitrumL1}}

{{CrossChainEnabledArbitrumL2}}

{{CrossChainEnabledOptimism}}

{{CrossChainEnabledPolygonChild}}

== Libraries for cross-chain

In addition to the {CrossChainEnabled} abstraction, cross-chain awareness is also available through libraries. These libraries can be used to build complex designs such as contracts with the ability to interact with multiple bridges.

{{LibAMB}}

{{LibArbitrumL1}}

{{LibArbitrumL2}}

{{LibOptimism}}
