name: Setup

runs:
  using: composite
  steps:
    - uses: actions/setup-node@v3
      with:
        node-version: 16.x
    - uses: actions/cache@v3
      id: cache
      with:
        path: '**/node_modules'
        key: npm-v3-${{ hashFiles('**/package-lock.json') }}
    - name: Install dependencies
      run: npm ci
      shell: bash
      if: steps.cache.outputs.cache-hit != 'true'
      env:
        SKIP_COMPILE: true
