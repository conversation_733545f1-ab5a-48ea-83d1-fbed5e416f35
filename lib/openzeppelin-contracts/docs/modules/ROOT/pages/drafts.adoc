= Drafts

All draft contracts were either moved into a different directory or removed from the OpenZeppelin Contracts library on the https://forum.openzeppelin.com/t/openzeppelin-contracts-v3-0-beta-release/2256[v3.0.0 release].

* `ERC20Migrator`: removed.
* xref:api:token/ERC20.adoc#ERC20Snapshot[`ERC20Snapshot`]: moved to `token/ERC20`.
* `ERC20Detailed` and `ERC1046`: removed.
* `TokenVesting`: removed. Pending a replacement that is being discussed in https://github.com/OpenZeppelin/openzeppelin-contracts/issues/1214[`#1214`].
* xref:api:utils.adoc#Counters[`Counters`]: moved to xref:api:utils.adoc[`utils`].
* xref:api:utils.adoc#Strings[`Strings`]: moved to xref:api:utils.adoc[`utils`].
* xref:api:utils.adoc#SignedSafeMath[`SignedSafeMath`]: moved to xref:api:utils.adoc[`utils`].

Removed contracts are still available on the v2.5 release of OpenZeppelin Contracts, which you can install by running:

```console
$ npm install @openzeppelin/contracts@v2.5
```

Refer to the xref:2.x@contracts:api:drafts.adoc[v2.x documentation] when working with them.
