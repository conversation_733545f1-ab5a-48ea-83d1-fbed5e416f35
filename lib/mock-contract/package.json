{"name": "@gnosis.pm/mock-contract", "version": "4.0.0", "description": "Simple Solidity contract to mock dependent contracts in truffle tests.", "main": "truffle-config.js", "files": ["contracts", "test"], "scripts": {"test-norpc": "truffle test", "test": "run-with-testrpc -l 20000000 --noVMErrorsOnRPCResponse true 'truffle test'"}, "keywords": ["solidity", "mock", "unit-testing", "ethereum", "truffle", "stub", "contract"], "homepage": "https://github.com/fleupold/mock-contract", "author": "<PERSON>", "license": "ISC", "devDependencies": {"run-with-testrpc": "^0.3.0", "truffle": "^5.1"}, "dependencies": {}}