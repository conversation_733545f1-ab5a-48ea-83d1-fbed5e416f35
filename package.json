{"name": "@flarenetwork/fasset", "version": "1.2.0-rc.1", "description": "Smart contracts implementing FAsset system.", "main": "", "repository": {"type": "git", "url": "git+https://github.com/flare-foundation/fassets.git"}, "author": "Flare Foundation", "license": "MIT", "directories": {}, "engines": {"node": ">=20"}, "files": ["artifacts", "contracts"], "scripts": {"---------TEST---SCRIPTS": "", "test": "yarn hardhat test", "coverage": "env NODE_OPTIONS=\"--max_old_space_size=8192\" yarn hardhat coverage --testfiles", "test-with-coverage": "yarn clean && yarn compile && yarn coverage \"test/unit test/integration\"", "cov": "yarn coverage", "testHH": "tsc && yarn hardhat test \"test/{unit,integration}/**/*.ts\"", "test_unit_hh": "env TEST_PATH=./test/unit yarn hardhat test", "test_integration_hh": "env TEST_PATH=./test/integration yarn hardhat test", "test_e2e": "yarn fasset_simulation", "fasset_simulation": "yarn test test/e2e-simulation/fasset/FAssetSimulation.ts", "tsrun": "yarn ts-node --files=./type-extensions.ts", "---------COMPILE---SCRIPTS": "", "clean": "env rm -rf cache artifacts cache-forge artifacts-forge build build-info typechain typechain-truffle", "clean-all": "yarn clean && env rm -rf node_modules", "compile": "yarn hardhat compile && yarn typechain-truffle-v5", "c": "yarn compile", "cl": "yarn compile && yarn lint", "lint": "yarn solhint \"contracts/**/*.sol\"", "lint-forge": "yarn solhint \"test-forge/**/*.sol\"", "solhint-watch": "node scripts/solhint-watch.js", "typechain-truffle-v5": "yarn typechain --target=truffle-v5 --out-dir typechain-truffle \"artifacts/!(build-info)/**/+([a-zA-Z0-9_]).json\"", "size": "yarn run hardhat size-contracts", "flatten": "yarn hardhat flatten", "install-slither": "which slither > /dev/null || PIP_BREAK_SYSTEM_PACKAGES=1 pip3 install slither-analyzer", "slither": "yarn install-slither; rm -f ./slither.json 2> /dev/null; slither . --json=./slither.json 2> /dev/null || true; node scripts/slither-parse.js ./slither.json", "slither-show-stderr": "yarn install-slither; rm -f ./slither.json 2> /dev/null; slither . --json=./slither.json || true; node scripts/slither-parse.js ./slither.json", "ts-compile-watch": "tsc --watch --noEmit", "eslint": "eslint", "generate-json-schema": "typescript-json-schema --noExtraProps --required --strictNullChecks", "generate-parameter-schema": "yarn generate-json-schema deployment/lib/asset-manager-parameters.ts AssetManagerParameters -o deployment/config/asset-manager-parameters.schema.json", "---------DEPLOY---SCRIPTS---HARDHAT": "", "local": "yarn hardhat --network local", "full-deploy-hardhat": "yarn local deploy-price-reader-v2 && yarn local deploy-asset-manager-dependencies --all && yarn local deploy-asset-managers --deploy-controller --all", "full-deploy-hardhat-test": "yarn local test --no-compile deployment/test/test-deployed-contracts.ts", "mock-deploy-hardhat": "rm -f deployment/deploys/hardhat.json && yarn local run deployment/test/scripts/mock-deploy-dependencies.ts && yarn local run deployment/test/scripts/mock-deploy-stablecoins.ts && yarn full-deploy-hardhat && yarn full-deploy-hardhat-test", "flare-sc-deploy-hardhat": "yarn local run deployment/test/scripts/mock-deploy-stablecoins.ts && yarn full-deploy-hardhat && yarn full-deploy-hardhat-test", "---------DEPLOY---SCRIPTS---COSTON": "", "coston": "yarn hardhat --network coston", "deploy-mock-stablecoins-coston": "yarn coston run deployment/test/scripts/mock-deploy-stablecoins.ts", "deploy-price-reader-v2-coston": "yarn coston deploy-price-reader-v2", "deploy-dependencies-coston": "yarn coston deploy-asset-manager-dependencies", "deploy-with-controller-coston": "yarn coston deploy-asset-managers --deploy-controller --all", "full-deploy-coston-test": "yarn coston test --no-compile deployment/test/test-deployed-contracts.ts", "verify-coston": "yarn coston verify-contract", "verify-asset-manager-coston": "yarn coston verify-asset-managers --all", "verify-asset-manager-controller-coston": "yarn coston verify-asset-manager-controller", "verify-asset-manager-facets-coston": "yarn coston verify-asset-manager-facets", "console": "yarn hardhat console --no-compile --network", "console-coston": "yarn hardhat console --no-compile --network coston", "---------DEPLOY---SCRIPTS---SONGBIRD": "", "songbird": "yarn hardhat --network songbird", "verify-songbird": "yarn songbird verify-contract", "console-songbird": "yarn hardhat console --no-compile --network songbird", "---------DEPLOY---SCRIPTS---COSTON2": "", "coston2": "yarn hardhat --network coston2", "verify-coston2": "yarn coston2 verify-contract", "console-coston2": "yarn hardhat console --no-compile --network coston2", "---------DEPLOY---SCRIPTS---FLARE": "", "flare": "yarn hardhat --network flare", "verify-flare": "yarn flare verify-contract", "console-flare": "yarn hardhat console --no-compile --network flare", "---------INFO---SCRIPTS": "", "gas-snapshot": "env CI=true yarn testHH; yarn gas-report", "gas-report": "ts-node scripts/process-gas-report.ts && cat .gas-report.txt", "gas-report-check": "scripts/gas-report-check.sh", "gas": "cat .gas-report.txt"}, "dependencies": {"@flarenetwork/flare-periphery-contracts": "0.1.30", "@openzeppelin/contracts": "4.9.6"}, "devDependencies": {"@eslint/compat": "^1.3.0", "@eslint/js": "^9.29.0", "@flarenetwork/js-flare-common": "^0.0.1", "@gnosis.pm/mock-contract": "4.0.0", "@nomicfoundation/hardhat-network-helpers": "1.0.12", "@nomicfoundation/hardhat-verify": "2.0.14", "@nomiclabs/hardhat-truffle5": "2.0.7", "@nomiclabs/hardhat-web3": "2.0.1", "@typechain/truffle-v5": "7.0.0", "@types/chai": "5.2.2", "@types/mocha": "10.0.10", "@types/node": "20.19.1", "chai": "4.5.0", "dotenv": "16.5.0", "eslint": "^9.29.0", "eth-sig-util": "3.0.1", "ethereumjs-util": "7.1.5", "glob": "11.0.3", "hardhat": "2.24.3", "hardhat-contract-sizer": "2.10.0", "hardhat-gas-reporter": "1.0.9", "intercept-stdout": "0.1.2", "solhint": "5.2.0", "solidity-coverage": "0.8.16", "ts-node": "10.9.2", "typechain": "7.0.1", "typescript": "5.8.3", "typescript-eslint": "^8.34.0", "typescript-json-schema": "0.59.0"}, "packageManager": "yarn@1.22.22"}