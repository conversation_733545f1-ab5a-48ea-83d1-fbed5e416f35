{"extends": "solhint:recommended", "plugins": [], "rules": {"avoid-call-value": "error", "avoid-low-level-calls": "error", "avoid-sha3": "error", "avoid-suicide": "error", "avoid-throw": "error", "avoid-tx-origin": "warn", "check-send-result": "error", "compiler-version": ["off", "0.8.23"], "const-name-snakecase": "error", "immutable-vars-naming": "off", "func-name-mixedcase": "error", "ordering": "off", "imports-on-top": "error", "max-line-length": ["error", 119], "max-states-count": ["error", 20], "multiple-sends": "error", "no-complex-fallback": "error", "no-inline-assembly": "error", "no-unused-vars": "error", "not-rely-on-block-hash": "error", "not-rely-on-time": "off", "payable-fallback": "error", "quotes": ["error", "double"], "reentrancy": "error", "state-visibility": "error", "use-forbidden-name": "error", "var-name-mixedcase": "error", "visibility-modifier-order": "error", "import-path-check": "error", "func-visibility": ["warn", {"ignoreConstructors": true}]}}